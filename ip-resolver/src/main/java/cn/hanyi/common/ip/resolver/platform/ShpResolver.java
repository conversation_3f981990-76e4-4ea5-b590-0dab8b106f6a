package cn.hanyi.common.ip.resolver.platform;

import cn.hanyi.common.ip.resolver.RegionInfo;
import org.apache.commons.lang3.NotImplementedException;
import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.factory.CommonFactoryFinder;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.index.strtree.STRtree;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.filter.FilterFactory2;


import java.util.HashMap;
import java.util.List;
import java.util.Map;



import java.io.IOException;


public class ShpResolver implements IpResolver {
    private SimpleFeatureSource featureSource;
    private GeometryFactory geometryFactory;
    private FilterFactory2 filterFactory;
    private DataStore dataStore;
    private STRtree spatialIndex;
    private boolean indexBuilt = false;
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(ShpResolver.class);
    /**
     * 初始化查询组件
     */
    private void initializeQueryComponents() throws Exception {
        // 1. 初始化几何和过滤器工厂
        geometryFactory = JTSFactoryFinder.getGeometryFactory();
        filterFactory = CommonFactoryFinder.getFilterFactory2();

        // 2. 加载Shapefile数据源
        java.net.URL url = getClass().getClassLoader().getResource("SHP/district.shp");
        if (url == null) {
            throw new RuntimeException("Shapefile not found: SHP/district.shp");
        }

        // 使用DataStore参数方式设置字符集，解决中文乱码问题
        Map<String, Object> params = new HashMap<>();
        params.put("url", url);
        params.put("charset", "UTF-8");

        this.dataStore = DataStoreFinder.getDataStore(params);
        if (this.dataStore == null) {
            throw new RuntimeException("无法打开 Shapefile: SHP/district.shp");
        }

        String typeName = this.dataStore.getTypeNames()[0];
        this.featureSource = this.dataStore.getFeatureSource(typeName);

        this.spatialIndex = new STRtree();
    }


    /**
     * 构建空间索引
     * 将所有要素的几何边界框加入STRtree索引中
     */
    private void buildSpatialIndex() throws Exception {
        if (indexBuilt) {
            return;
        }

        SimpleFeatureCollection features = featureSource.getFeatures();
        try (org.geotools.data.simple.SimpleFeatureIterator iterator = features.features()) {
            while (iterator.hasNext()) {
                SimpleFeature feature = iterator.next();
                Geometry geometry = (Geometry) feature.getDefaultGeometry();
                if (geometry != null) {
                    Envelope envelope = geometry.getEnvelopeInternal();
                    spatialIndex.insert(envelope, feature);
                }
            }
        }

        spatialIndex.build();
        indexBuilt = true;

    }



    /**
     * 根据经纬度查询行政区划信息（使用空间索引优化）
     *
     * @param lng 经度
     * @param lat 纬度
     * @return 包含行政区划信息的SimpleFeature，如果未找到则返回null
     */
    public SimpleFeature queryByCoordinate(double lng, double lat) throws Exception {
        // 确保空间索引已构建
        buildSpatialIndex();

        // 创建查询点
        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));

        // 使用空间索引进行快速预筛选
        List<SimpleFeature> candidates = spatialIndex.query(point.getEnvelopeInternal());

        // 在候选要素中进行精确的几何包含测试
        for (SimpleFeature candidate : candidates) {
            Geometry geometry = (Geometry) candidate.getDefaultGeometry();
            if (geometry != null && geometry.contains(point)) {
                return candidate;
            }
        }

        return null;
    }


    @Override
    public RegionInfo resolveRegion(String ip){
        throw new NotImplementedException("unsupported ip query");
    }

    @Override
    public RegionInfo resolveLocation(Float latitude, Float longitude) throws IOException {
        RegionInfo info = RegionInfo.builder().country("中国").province("未知").city("未知").district("未知").build();
        try {
            initializeQueryComponents();
            SimpleFeature feature = queryByCoordinate(longitude, latitude);
            if (feature != null) {
                info.setCountry(feature.getAttribute("cn_name").toString());
                info.setProvince(feature.getAttribute("pr_name").toString());
                info.setCity(feature.getAttribute("ct_name").toString());
                info.setDistrict(feature.getAttribute("dt_name").toString());
            }
        } catch (Exception e) {
            log.error("Failed to resolve region: {}", e.getMessage());
        }
        return info;
    }

    public static void main(String[] args) {
        try {
            ShpResolver resolver = new ShpResolver();
            RegionInfo regionInfo = resolver.resolveLocation(39.904989f, 116.405285f);
            System.out.println(regionInfo);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}


